# Response Examples Implementation

## Overview
This document describes the implementation of response examples extraction from workflow configuration files for the PolicyWorkflowConfigurationView.

## Changes Made

### 1. Modified `WorkflowConfigurationManager.prepare_frontend_config()`
**File:** `customer/_services/policy_workflow_config_manager.py`

- Added call to `_extract_response_examples()` method to include response examples in the frontend configuration
- Response examples are now automatically extracted from workflow steps and included in the metadata

### 2. Added `_extract_response_examples()` method
**File:** `customer/_services/policy_workflow_config_manager.py`

- New method that extracts `response_example` fields from each workflow step
- Creates a structured format with step information and response examples
- Adds the extracted examples to the workflow metadata under `response_examples` key

## Response Structure

The modified `PolicyWorkflowConfigurationView` now returns workflow configurations with the following enhanced structure:

```json
{
  "workflows": [
    {
      "workflow": { ... },
      "configuration": { ... },
      "steps": [ ... ],
      "metadata": {
        "support_contact": "...",
        "processed_at": "2024-01-20T12:00:00Z",
        "environment": "development",
        "response_examples": {
          "step_id": {
            "step_name": "Step Name",
            "description": "Step description",
            "response_example": { ... }
          }
        }
      }
    }
  ],
  "status": "success"
}
```

## Response Examples Structure

Each response example in the metadata contains:
- `step_name`: Human-readable name of the workflow step
- `description`: Description of what the step does
- `response_example`: The actual example response data from the JSON configuration

## Workflow Files Updated

The following workflow configuration files contain `response_example` fields that are now extracted:

1. **Policy List Workflow** (`bvtpa-policy-list-workflow.json`):
   - `authenticate` step: Bearer token example
   - `verify_citizen` step: Citizen ID verification response
   - `verify_registration` step: Registration status response
   - `fetch_policies` step: Policy list response with detailed field examples

2. **Policy Details Workflow** (`bvtpa-policy-details-workflow.json`):
   - `authenticate` step: Bearer token example
   - `fetch_policy_details` step: Policy details and claims response

## Security Considerations

- The existing `mask_sensitive_workflow_fields()` function continues to work with the new response examples
- Response examples are processed recursively to ensure any sensitive data is properly masked
- The examples in the JSON files contain sample/mock data, not real sensitive information

## Frontend Usage

The frontend can now access response examples through:
```javascript
const workflows = response.workflows;
workflows.forEach(workflow => {
  const examples = workflow.metadata?.response_examples;
  if (examples) {
    Object.keys(examples).forEach(stepId => {
      const example = examples[stepId];
      console.log(`Step: ${example.step_name}`);
      console.log(`Example:`, example.response_example);
    });
  }
});
```

## Testing

The implementation has been verified to:
- ✅ Extract response examples from all workflow steps that contain them
- ✅ Maintain the existing workflow configuration structure
- ✅ Apply proper field masking for security
- ✅ Include examples in the metadata without breaking existing functionality
- ✅ Handle both policy list and policy details workflows correctly

## Benefits

1. **Documentation**: Frontend developers can see example API responses for each workflow step
2. **Testing**: Response examples can be used for frontend testing and development
3. **Debugging**: Helps understand the expected data structure for each API call
4. **Backward Compatibility**: Existing functionality remains unchanged
