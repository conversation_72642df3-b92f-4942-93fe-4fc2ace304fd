"""
Enhanced workflow configuration management for the refactored policy workflow system.
This module provides configuration-driven approach to replace hardcoded constants.
"""

import os
import json
import re
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from .policy_workflow_registry import get_workflow_registry

logger = logging.getLogger('django.customer_policy_crm')


@dataclass
class ApiConfiguration:
    """API configuration data class"""
    base_url: str
    timeout_seconds: int
    ssl_verify: bool
    max_retries: int
    retry_delay_seconds: int
    backoff_strategy: str
    endpoints: Dict[str, str]


@dataclass
class CacheConfiguration:
    """Cache configuration data class"""
    enabled: bool
    duration_minutes: int
    key_template: str


class WorkflowConfigurationManager:
    """
    Manages workflow configuration from enhanced JSON files.
    Handles environment-specific settings and variable substitution.
    """
    
    def __init__(self, environment: Optional[str] = None):
        self.environment = environment or self._detect_environment()
        self._config_cache = {}
        logger.info(f"WorkflowConfigurationManager initialized for environment: {self.environment}")
    
    def _detect_environment(self) -> str:
        """Detect current environment from environment variables"""
        env = os.getenv('DJANGO_ENV', os.getenv('ENVIRONMENT', 'development')).lower() # Default to 'development' if not found
        logger.debug(f"WorkflowConfigurationManager detected environment: {env}")
        return env
    
    def _substitute_variables(self, value: Any, context: Dict[str, Any] = None) -> Any:
        """
        Substitute environment variables and context variables in configuration values.
        Supports format: ${VAR_NAME:default_value}
        """
        if not isinstance(value, str):
            return value
        
        # Pattern for ${VAR_NAME:default_value} or ${VAR_NAME}
        pattern = r'\$\{([^:}]+)(?::([^}]*))?\}'
        
        def replace_var(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) is not None else ''
            
            # Try environment variable first
            env_value = os.getenv(var_name)
            if env_value is not None:
                return env_value
            
            # Try context variable
            if context and var_name in context:
                return str(context[var_name])
            
            # Use default value
            return default_value
        
        return re.sub(pattern, replace_var, value)
    
    def _apply_environment_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment-specific configuration overrides"""
        base_config = config.copy()
        
        # Get environment-specific overrides
        env_config = config.get('configuration', {}).get('environments', {}).get(self.environment, {})
        
        if env_config:
            logger.debug(f"WorkflowConfigurationManager applying environment overrides for: {self.environment}")
            # Deep merge environment configuration
            configuration = base_config.get('configuration', {})
            
            # Merge API configuration
            if 'api' in env_config:
                api_config = configuration.get('api', {})
                api_config.update(env_config['api'])
                configuration['api'] = api_config
            
            # Merge cache configuration
            if 'cache' in env_config:
                cache_config = configuration.get('execution', {}).get('cache', {})
                cache_config.update(env_config['cache'])
                if 'execution' not in configuration:
                    configuration['execution'] = {}
                configuration['execution']['cache'] = cache_config
            
            # Add credentials to configuration if provided
            if 'credentials' in env_config:
                configuration['credentials'] = env_config['credentials']
            
            base_config['configuration'] = configuration
        
        return base_config
    
    def _substitute_config_variables(self, obj: Any, config: Dict[str, Any]) -> Any:
        """Recursively substitute configuration variables in the format {{config.path.to.value}}"""
        if isinstance(obj, dict):
            return {k: self._substitute_config_variables(v, config) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_config_variables(item, config) for item in obj]
        elif isinstance(obj, str):
            # Handle {{config.path.to.value}} substitution
            pattern = r'\{\{config\.([^}]+)\}\}'
            
            def replace_config_var(match):
                path = match.group(1)
                value = self._get_nested_value(config.get('configuration', {}), path)
                return str(value) if value is not None else match.group(0)
            
            return re.sub(pattern, replace_config_var, obj)
        else:
            return obj
    
    def _get_nested_value(self, obj: Dict[str, Any], path: str) -> Any:
        """Get nested value from dictionary using dot notation"""
        keys = path.split('.')
        current = obj
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def load_workflow_config(self, workflow_id: str) -> Dict[str, Any]:
        """Load and process workflow configuration"""
        if workflow_id in self._config_cache:
            logger.debug(f"WorkflowConfigurationManager returning cached config for: {workflow_id}")
            return self._config_cache[workflow_id]
        
        logger.info(f"WorkflowConfigurationManager loading config for workflow: {workflow_id}")
        
        # Load base configuration from registry
        config = self._load_raw_config(workflow_id)
        
        # Apply environment overrides
        config = self._apply_environment_overrides(config)
        
        # Substitute environment variables
        config = self._substitute_variables_recursive(config)
        
        # Substitute configuration references
        config = self._substitute_config_variables(config, config)
        
        # Cache the processed configuration
        self._config_cache[workflow_id] = config
        
        logger.info(f"WorkflowConfigurationManager successfully loaded and processed config for: {workflow_id}")
        return config
    
    def _load_raw_config(self, workflow_id: str) -> Dict[str, Any]:
        """Load raw configuration from workflow registry"""
        registry = get_workflow_registry()
        config = registry.get_workflow(workflow_id)
        
        if not config:
            logger.error(f"WorkflowConfigurationManager: Workflow configuration not found: {workflow_id}")
            raise ValueError(f"Workflow configuration not found: {workflow_id}")
        
        return config
    
    def _substitute_variables_recursive(self, obj: Any) -> Any:
        """Recursively substitute environment variables in configuration"""
        if isinstance(obj, dict):
            return {k: self._substitute_variables_recursive(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_variables_recursive(item) for item in obj]
        elif isinstance(obj, str):
            return self._substitute_variables(obj)
        else:
            return obj
    
    def get_api_config(self, workflow_config: Dict[str, Any]) -> ApiConfiguration:
        """Extract API configuration from workflow config"""
        api_config = workflow_config.get('configuration', {}).get('api', {})
        
        return ApiConfiguration(
            base_url=api_config.get('base_url', 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2'),
            timeout_seconds=api_config.get('timeout_seconds', 10),
            ssl_verify=api_config.get('ssl_verify', False),
            max_retries=api_config.get('max_retries', 3),
            retry_delay_seconds=api_config.get('retry_delay_seconds', 3),
            backoff_strategy=api_config.get('backoff_strategy', 'exponential'),
            endpoints=api_config.get('endpoints', {})
        )
    
    def get_cache_config(self, workflow_config: Dict[str, Any]) -> CacheConfiguration:
        """Extract cache configuration from workflow config"""
        cache_config = workflow_config.get('configuration', {}).get('execution', {}).get('cache', {})
        
        return CacheConfiguration(
            enabled=cache_config.get('enabled', True),
            duration_minutes=cache_config.get('duration_minutes', 60),
            key_template=cache_config.get('key_template', 'default_{workflow_id}')
        )
    
    def get_response_fields(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract response field mappings from workflow config"""
        return workflow_config.get('configuration', {}).get('response_fields', {})
    
    def get_error_messages(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract error message templates from workflow config"""
        return workflow_config.get('configuration', {}).get('error_messages', {})
    
    def get_validation_config(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract validation configuration from workflow config"""
        return workflow_config.get('configuration', {}).get('validation', {})
    
    def get_credentials(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract credentials from workflow config"""
        return workflow_config.get('configuration', {}).get('credentials', {})

    def prepare_frontend_config(self, workflow_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare workflow configuration for frontend consumption with additional formatting.
        This method ensures all template variables are resolved and adds frontend-friendly metadata.
        """
        if not workflow_config:
            return None

        # The configuration is already processed by load_workflow_config, but we can add
        # additional frontend-specific formatting here if needed
        frontend_config = workflow_config.copy()

        # Add metadata for frontend display
        if 'metadata' not in frontend_config:
            frontend_config['metadata'] = {}

        # Add processing timestamp
        from django.utils import timezone
        frontend_config['metadata']['processed_at'] = timezone.now().isoformat()
        frontend_config['metadata']['environment'] = self.environment

        # Extract and include response examples from workflow steps
        self._extract_response_examples(frontend_config)

        # Ensure all numeric values in retry configurations are properly typed
        self._ensure_numeric_types(frontend_config)

        return frontend_config

    def _extract_response_examples(self, config: Dict[str, Any]) -> None:
        """
        Extract response examples from workflow steps and add them to metadata for frontend display.
        This provides the frontend with example API responses for documentation and testing purposes.
        """
        response_examples = {}

        # Extract response examples from each step
        steps = config.get('steps', [])
        for step in steps:
            step_id = step.get('id')
            step_name = step.get('name', step_id)
            step_config = step.get('config', {})

            if 'response_example' in step_config:
                response_examples[step_id] = {
                    'step_name': step_name,
                    'description': step.get('description', ''),
                    'response_example': step_config['response_example']
                }

        # Add response examples to metadata if any were found
        if response_examples:
            if 'metadata' not in config:
                config['metadata'] = {}
            config['metadata']['response_examples'] = response_examples

    def _ensure_numeric_types(self, config: Dict[str, Any]) -> None:
        """Ensure numeric values are properly typed for frontend consumption"""
        # Convert string numbers to integers/floats where appropriate
        numeric_paths = [
            ['configuration', 'api', 'timeout_seconds'],
            ['configuration', 'api', 'max_retries'],
            ['configuration', 'api', 'retry_delay_seconds'],
            ['configuration', 'execution', 'timeout_minutes'],
            ['configuration', 'execution', 'retry_policy', 'default_max_retries'],
            ['configuration', 'execution', 'retry_policy', 'default_delay_seconds'],
            ['configuration', 'execution', 'cache', 'duration_minutes']
        ]

        for path in numeric_paths:
            self._convert_to_numeric(config, path)

        # Also handle step-level retry configurations
        steps = config.get('steps', [])
        for step in steps:
            if isinstance(step, dict) and 'config' in step and 'retry' in step['config']:
                retry_config = step['config']['retry']
                if 'max_attempts' in retry_config:
                    retry_config['max_attempts'] = self._to_int(retry_config['max_attempts'])
                if 'delay_seconds' in retry_config:
                    retry_config['delay_seconds'] = self._to_int(retry_config['delay_seconds'])

    def _convert_to_numeric(self, config: Dict[str, Any], path: List[str]) -> None:
        """Convert a value at the given path to numeric if it's a string number"""
        current = config
        for key in path[:-1]:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return

        if isinstance(current, dict) and path[-1] in current:
            current[path[-1]] = self._to_int(current[path[-1]])

    def _to_int(self, value: Any) -> Any:
        """Convert string numbers to integers, leave other types unchanged"""
        if isinstance(value, str) and value.isdigit():
            return int(value)
        elif isinstance(value, str):
            try:
                return float(value)
            except ValueError:
                return value
        return value


# Global configuration manager instance
_config_manager_instance: Optional[WorkflowConfigurationManager] = None


def get_workflow_configuration_manager() -> WorkflowConfigurationManager:
    """Get the global workflow configuration manager instance (singleton)"""
    global _config_manager_instance
    
    if _config_manager_instance is None:
        _config_manager_instance = WorkflowConfigurationManager()
    
    return _config_manager_instance


def reset_workflow_configuration_manager():
    """Reset the global configuration manager (mainly for testing)"""
    global _config_manager_instance
    _config_manager_instance = None
